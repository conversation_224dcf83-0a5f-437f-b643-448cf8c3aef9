import React, { useEffect, useState } from 'react';
import { HashRouter as Router, Routes, Route } from 'react-router-dom';
import HomePage from './pages/HomePage';
import ChatPage from './pages/ChatPage';
import SettingsPage from './pages/SettingsPage';
import { migrateFromLocalStorage, clearLocalStorageAfterMigration } from './utils/migrationUtils';
import indexedDBService from './services/indexedDBService';
import migrationStyles from './styles/Migration.module.css';

const App: React.FC = () => {
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationComplete, setMigrationComplete] = useState(false);
  const [dbReady, setDbReady] = useState(false);
  const [initTimeout, setInitTimeout] = useState(false);

  // Initialize IndexedDB and migrate data
  useEffect(() => {
    const initializeApp = async () => {
      console.log('App initialization started...');
      try {
        // Initialize IndexedDB
        console.log('Initializing IndexedDB...');
        await indexedDBService.init();
        console.log('IndexedDB initialized successfully');
        setDbReady(true);

        // Check if we need to migrate data
        const migrationCompleted = localStorage.getItem('rohit_migration_completed');
        console.log('Migration completed flag:', migrationCompleted);

        if (!migrationCompleted) {
          console.log('Starting migration process...');
          setIsMigrating(true);
          const migrationSuccess = await migrateFromLocalStorage();
          console.log('Migration success:', migrationSuccess);

          if (migrationSuccess) {
            // Clean up localStorage after successful migration
            // Only remove the old conversations data, keep the migration flag
            clearLocalStorageAfterMigration();
            console.log('Migration cleanup completed');
          }

          setIsMigrating(false);
          setMigrationComplete(true);
          console.log('Migration process completed');
        } else {
          console.log('Migration already completed, skipping...');
          setMigrationComplete(true);
        }

        console.log('App initialization completed successfully');
      } catch (error) {
        console.error('Error initializing app:', error);
        setIsMigrating(false);
        // Set migration complete to true even on error to prevent infinite loading
        setMigrationComplete(true);
        // Set dbReady to true to allow app to continue even if IndexedDB fails
        setDbReady(true);
        console.log('App initialization completed with errors, continuing anyway...');
      }
    };

    // Set up a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      console.warn('App initialization timeout reached, forcing app to continue...');
      setInitTimeout(true);
      setDbReady(true);
      setMigrationComplete(true);
      setIsMigrating(false);
    }, 10000); // 10 second timeout

    initializeApp().finally(() => {
      clearTimeout(timeoutId);
    });

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  // Show loading/migration screen if needed
  if ((!dbReady || isMigrating) && !initTimeout) {
    return (
      <div className={migrationStyles.migrationContainer}>
        <div className={migrationStyles.migrationTitle}>
          {!dbReady ? 'Initializing App...' : 'Migrating Conversations...'}
        </div>
        <div className={migrationStyles.progressBarContainer}>
          <div className={isMigrating ? migrationStyles.indeterminateProgress : migrationStyles.completeProgress} />
        </div>
        <div className={migrationStyles.statusText}>
          {isMigrating ? 'Moving your conversations to improved storage...' : 'Almost ready...'}
          {initTimeout && <div style={{ color: '#ff6b6b', marginTop: '10px' }}>Taking longer than expected, continuing anyway...</div>}
        </div>
      </div>
    );
  }
  return (
    <Router>
      <div className="app-container">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/chat" element={<ChatPage />} />
          <Route path="/settings" element={<SettingsPage />} />
        </Routes>
      </div>
    </Router>
  );
};

export default App;