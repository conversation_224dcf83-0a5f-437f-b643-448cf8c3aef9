/**
 * Memory management utilities for conversation context
 */

export const DEFAULT_MEMORY_LIMIT = 10;
export const MIN_MEMORY_LIMIT = 1;
export const MAX_MEMORY_LIMIT = 30;

/**
 * Gets the user's selected memory limit from localStorage
 */
export function getMemoryLimit(): number {
  const stored = localStorage.getItem('chat_memory_limit');
  if (stored) {
    const parsed = parseInt(stored, 10);
    if (!isNaN(parsed)) {
      return Math.min(Math.max(parsed, MIN_MEMORY_LIMIT), MAX_MEMORY_LIMIT);
    }
  }
  return DEFAULT_MEMORY_LIMIT;
}

/**
 * Sets the user's memory limit in localStorage
 */
export function setMemoryLimit(limit: number): void {
  const validatedLimit = Math.min(Math.max(limit, MIN_MEMORY_LIMIT), MAX_MEMORY_LIMIT);
  localStorage.setItem('chat_memory_limit', validatedLimit.toString());
  
  // Also update in settings object for consistency
  const savedSettings = localStorage.getItem('rohit_settings');
  const settings = savedSettings ? JSON.parse(savedSettings) : {};
  settings.chatMemory = validatedLimit;
  localStorage.setItem('rohit_settings', JSON.stringify(settings));
}

/**
 * Gets memory impact description based on limit
 */
export function getMemoryImpact(limit: number): {
  context: string;
  performance: string;
  description: string;
} {
  if (limit <= 5) {
    return {
      context: 'Minimal',
      performance: 'Fast',
      description: 'AI remembers only the most recent messages. Best for quick questions or when performance is critical.'
    };
  } else if (limit <= 15) {
    return {
      context: 'Balanced',
      performance: 'Good',
      description: 'AI remembers a moderate amount of context. Good balance between context awareness and performance.'
    };
  } else {
    return {
      context: 'Maximum',
      performance: 'Slower',
      description: 'AI remembers extensive conversation history. Best for complex, long-form discussions.'
    };
  }
}

/**
 * Gets memory preset configurations
 */
export function getMemoryPresets(): Array<{
  name: string;
  value: number;
  description: string;
}> {
  return [
    {
      name: 'Short',
      value: 3,
      description: 'Minimal context, fastest responses'
    },
    {
      name: 'Medium',
      value: 10,
      description: 'Balanced context and performance'
    },
    {
      name: 'Long',
      value: 20,
      description: 'Extended context, slower responses'
    }
  ];
}

/**
 * Calculates the actual number of messages that will be sent to the AI
 * based on the current conversation and memory limit
 */
export function calculateContextSize(totalMessages: number, memoryLimit?: number): {
  contextSize: number;
  memoryLimit: number;
  isLimited: boolean;
} {
  const limit = memoryLimit ?? getMemoryLimit();
  const contextSize = Math.min(totalMessages, limit);
  
  return {
    contextSize,
    memoryLimit: limit,
    isLimited: totalMessages > limit
  };
}

/**
 * Formats memory context information for display
 */
export function formatMemoryContext(totalMessages: number, memoryLimit?: number): string {
  const { contextSize, isLimited } = calculateContextSize(totalMessages, memoryLimit);
  
  if (totalMessages === 0) {
    return 'No conversation history';
  }
  
  if (isLimited) {
    return `Using ${contextSize} of ${totalMessages} messages`;
  } else {
    return `Using all ${contextSize} messages`;
  }
}
